import { Injectable, BadRequestException } from '@nestjs/common';
import { AuthRoleService } from '../auth-role.service';
import { OAuthUserData } from './oauth.service';
import { AuthCredentialRepository, RoleRepository, UserRepository } from '@shared/shared/repositories';
import { AuthProvider, AuthRole } from '@shared/shared/common/constants/constants';
import { User } from '@shared/shared/repositories/models/user.model';
import { OnboardingStep } from '@shared/shared/repositories/models/userOnboard.model';
import { UserOnboardingService } from '../../user-onboarding/user-onboarding.service';

@Injectable()
export class UserRegistrationService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly authCredentialRepository: AuthCredentialRepository,
    private readonly roleRepository: RoleRepository,
    private readonly authRoleService: AuthRoleService,
    private readonly userOnboardingService: UserOnboardingService,
  ) { }

  /**
   * Register a new user with phone number
   * @param phoneNumber Phone number in international format
   * @param role User role (rider, driver, etc.)
   * @returns Created or existing user
   */
  async registerWithPhone(phoneNumber: string, role: AuthRole): Promise<User> {
    // First check if an auth credential with this phone number already exists
    const existingCredential =
      await this.authCredentialRepository.findByTypeAndIdentifier(
        AuthProvider.PHONE,
        phoneNumber,
      );

    let user: User | null = null;

    if (existingCredential) {
      // Check if the user associated with this credential exists (including soft-deleted)
      const credentialUser = await this.userRepository.findById(
        existingCredential.userId,
        { includeSoftDeleted: true },
      );

      if (credentialUser) {
        if (credentialUser.deletedAt) {
          // If user was soft-deleted, restore it
          console.log(
            `Restoring previously deleted user with ID ${credentialUser.id}`,
          );
          const restoredUser = await this.userRepository.restoreById(
            credentialUser.id,
          );
          user = restoredUser as User;
        } else {
          // User exists and is not deleted, use it
          user = credentialUser as User;
        }
      }
    }

    // If no user was found or restored, create a new one
    if (!user) {
      const newUser = await this.userRepository.create({
        phoneNumber,
        email: null,
        emailVerifiedAt: null,
        phoneVerifiedAt: null,
        otpSecret: null,
        isPolicyAllowed: false,
      });

      user = newUser as User;

      // Use createOrLinkCredential which handles the case where a credential already exists
      await this.authCredentialRepository.createOrLinkCredential(
        AuthProvider.PHONE,
        phoneNumber,
        user.id,
        null, // No metadata needed for phone auth
      );

      await this.authRoleService.assignRoleToUser(user.id, role);

      if (role === AuthRole.DRIVER) {
        // Automatically create onboarding step for driver
        const driverRole = await this.roleRepository.findByName(role);
        if (driverRole) {
          await this.userOnboardingService.updateOrCreateOnboardingStepByUserAndRole(
            user.id,
            driverRole.id,
            OnboardingStep.PHONE_VERIFICATION,
          );
        }
      }
    }

    return user;
  }

  /**
   * Register a new user with email address (riders only)
   * @param email Email address
   * @param role User role (must be RIDER)
   * @returns Created or existing user
   */
  async registerWithEmail(email: string, role: AuthRole): Promise<User> {
    // Check if role is RIDER, if not, throw an error
    if (role !== AuthRole.RIDER) {
      throw new BadRequestException(
        'Email authentication is only available for riders',
      );
    }

    let user = await this.userRepository.findByEmail(email);

    if (!user) {
      user = await this.userRepository.create({
        email,
        emailVerifiedAt: null,
        phoneNumber: null,
        phoneVerifiedAt: null,
        otpSecret: null,
        isPolicyAllowed: false,
      });

      await this.authCredentialRepository.createWithUser(
        user.id,
        AuthProvider.EMAIL,
        email,
      );

      await this.authRoleService.assignRoleToUser(user.id, role);
    }

    return user as User;
  }

  /**
   * Register or find user with OAuth provider
   * @param provider OAuth provider
   * @param userData User data from OAuth provider
   * @param role Role for the user
   * @returns Created or existing user
   */
  async registerWithOAuth(
    provider: AuthProvider,
    userData: OAuthUserData,
    role?: AuthRole,
  ): Promise<User> {
    if (!userData.email) {
      throw new BadRequestException('Email is required for OAuth registration');
    }

    let user = await this.userRepository.findByEmail(userData.email);

    if (!user) {
      // Create new user
      user = await this.userRepository.create({
        email: userData.email,
        emailVerifiedAt: new Date(),
        phoneNumber: null,
        phoneVerifiedAt: null,
        otpSecret: null,
        isPolicyAllowed: false,
      });

      // Create auth credential for OAuth provider
      const providerId = userData.sub || userData.id;
      if (!providerId) {
        throw new BadRequestException('Provider user ID is required for OAuth registration');
      }

      await this.authCredentialRepository.createWithUser(
        user.id,
        provider,
        providerId,
      );

      // Assign role from parameter or default to RIDER
      await this.authRoleService.assignRoleToUser(
        user.id,
        role || AuthRole.RIDER,
      );
    }

    return user as User;
  }
}
